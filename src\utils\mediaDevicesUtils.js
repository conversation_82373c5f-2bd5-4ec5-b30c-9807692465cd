/**
 * Media Devices Utilities
 * 
 * Provides safe access to navigator.mediaDevices API with proper error handling
 * and fallbacks for environments where the API is not available.
 */

/**
 * Check if the MediaDevices API is available
 * @returns {boolean} True if the API is available
 */
export const isMediaDevicesSupported = () => {
  return !!(navigator.mediaDevices && 
           navigator.mediaDevices.getUserMedia && 
           navigator.mediaDevices.enumerateDevices);
};

/**
 * Get a detailed error message for MediaDevices API issues
 * @returns {string} Human-readable error message
 */
export const getMediaDevicesErrorMessage = () => {
  if (!navigator.mediaDevices) {
    return 'MediaDevices API not available. Please ensure you\'re using HTTPS and a modern browser.';
  }
  
  if (!navigator.mediaDevices.getUserMedia) {
    return 'getUserMedia not available. Please update your browser or check permissions.';
  }
  
  if (!navigator.mediaDevices.enumerateDevices) {
    return 'enumerateDevices not available. Please update your browser.';
  }
  
  return 'Unknown MediaDevices API issue.';
};

/**
 * Safely enumerate audio devices
 * @returns {Promise<Array>} Array of audio input devices
 */
export const safeEnumerateAudioDevices = async () => {
  if (!isMediaDevicesSupported()) {
    throw new Error(getMediaDevicesErrorMessage());
  }
  
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    return devices.filter(device => device.kind === 'audioinput');
  } catch (error) {
    throw new Error(`Failed to enumerate devices: ${error.message}`);
  }
};

/**
 * Safely request microphone access
 * @param {Object} constraints - Audio constraints
 * @returns {Promise<MediaStream>} Media stream
 */
export const safeGetUserMedia = async (constraints = { audio: true }) => {
  if (!isMediaDevicesSupported()) {
    throw new Error(getMediaDevicesErrorMessage());
  }
  
  try {
    return await navigator.mediaDevices.getUserMedia(constraints);
  } catch (error) {
    // Provide more helpful error messages
    if (error.name === 'NotAllowedError') {
      throw new Error('Microphone access denied. Please allow microphone access and try again.');
    } else if (error.name === 'NotFoundError') {
      throw new Error('No microphone found. Please connect a microphone and try again.');
    } else if (error.name === 'NotReadableError') {
      throw new Error('Microphone is already in use by another application.');
    } else if (error.name === 'OverconstrainedError') {
      throw new Error('Microphone constraints cannot be satisfied.');
    } else {
      throw new Error(`Microphone access failed: ${error.message}`);
    }
  }
};

/**
 * Test microphone access without keeping the stream
 * @param {Object} constraints - Audio constraints
 * @returns {Promise<boolean>} True if microphone access is available
 */
export const testMicrophoneAccess = async (constraints = { audio: true }) => {
  try {
    const stream = await safeGetUserMedia(constraints);
    // Immediately stop all tracks
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    console.warn('Microphone test failed:', error.message);
    return false;
  }
};

/**
 * Get detailed information about the browser's media capabilities
 * @returns {Object} Browser media capabilities info
 */
export const getBrowserMediaInfo = () => {
  const info = {
    hasNavigator: typeof navigator !== 'undefined',
    hasMediaDevices: !!(navigator && navigator.mediaDevices),
    hasGetUserMedia: !!(navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
    hasEnumerateDevices: !!(navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices),
    isSecureContext: typeof window !== 'undefined' ? window.isSecureContext : false,
    protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
  };
  
  info.isSupported = info.hasGetUserMedia && info.hasEnumerateDevices;
  info.requiresHTTPS = info.protocol !== 'https:' && info.protocol !== 'file:';
  
  return info;
};

/**
 * Log detailed browser media capabilities for debugging
 */
export const logBrowserMediaCapabilities = () => {
  const info = getBrowserMediaInfo();
  
  console.group('🎤 Browser Media Capabilities');
  console.log('Navigator available:', info.hasNavigator);
  console.log('MediaDevices available:', info.hasMediaDevices);
  console.log('getUserMedia available:', info.hasGetUserMedia);
  console.log('enumerateDevices available:', info.hasEnumerateDevices);
  console.log('Secure context:', info.isSecureContext);
  console.log('Protocol:', info.protocol);
  console.log('Fully supported:', info.isSupported);
  
  if (info.requiresHTTPS) {
    console.warn('⚠️ HTTPS required for MediaDevices API');
  }
  
  if (!info.isSupported) {
    console.error('❌ MediaDevices API not fully supported');
    console.log('Suggested fixes:');
    if (info.requiresHTTPS) {
      console.log('- Use HTTPS instead of HTTP');
    }
    if (!info.hasMediaDevices) {
      console.log('- Update to a modern browser');
    }
  } else {
    console.log('✅ MediaDevices API fully supported');
  }
  
  console.groupEnd();
  
  return info;
};

/**
 * Create a user-friendly error message for MediaDevices issues
 * @param {Error} error - The original error
 * @returns {string} User-friendly error message
 */
export const createUserFriendlyMediaError = (error) => {
  const info = getBrowserMediaInfo();
  
  if (!info.isSupported) {
    if (info.requiresHTTPS) {
      return 'Microphone access requires a secure connection (HTTPS). Please contact support if this issue persists.';
    }
    return 'Your browser doesn\'t support microphone access. Please update your browser or try a different one.';
  }
  
  // Handle specific error types
  if (error.message.includes('MediaDevices API not available')) {
    return 'Microphone access is not available. Please ensure you\'re using a secure connection and a modern browser.';
  }
  
  if (error.message.includes('denied') || error.name === 'NotAllowedError') {
    return 'Microphone access was denied. Please allow microphone access in your browser settings and try again.';
  }
  
  if (error.message.includes('not found') || error.name === 'NotFoundError') {
    return 'No microphone was found. Please connect a microphone and try again.';
  }
  
  if (error.name === 'NotReadableError') {
    return 'Your microphone is already being used by another application. Please close other applications and try again.';
  }
  
  return `Microphone access failed: ${error.message}`;
};
